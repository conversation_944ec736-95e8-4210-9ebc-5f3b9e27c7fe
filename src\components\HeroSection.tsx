"use client";
import React from 'react';

interface HeroSectionProps {
  title?: string;
  subtitle?: string;
  primaryButtonText?: string;
  primaryButtonHref?: string;
  secondaryButtonText?: string;
  secondaryButtonHref?: string;
  className?: string;
}

// Diverse images for the mosaic background
const MOSAIC_IMAGES = [
  "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1618221195710-dd6b41faaea6?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1616486338812-3dadae4b4ace?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1615529328331-f8917597711f?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1616137466211-f939a420be84?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1615874959474-d609969a20ed?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1631679706909-1844bbd07221?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1616594039964-ae9021a400a0?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1615529182904-14819c35db37?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1616137466211-f939a420be84?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1615529182904-14819c35db37?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1616594039964-ae9021a400a0?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1631679706909-1844bbd07221?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1615874959474-d609969a20ed?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1616137466211-f939a420be84?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1615529328331-f8917597711f?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1616486338812-3dadae4b4ace?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1618221195710-dd6b41faaea6?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1631679706909-1844bbd07221?w=300&h=300&fit=crop",
];

export const HeroSection: React.FC<HeroSectionProps> = ({
  title = "Professional Production Design",
  subtitle = "Create authentic sets for TV series, movies, and entertainment productions. Professional design tools with 110+ styles and cinematic environments.",
  primaryButtonText = "Start Designing",
  primaryButtonHref = "/design/new",
  secondaryButtonText = "View Gallery",
  secondaryButtonHref = "/gallery",
  className = ""
}) => {
  return (
    <section className={`relative min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950 overflow-hidden ${className}`}>
      <style jsx>{`
        @keyframes scroll-left {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-100%);
          }
        }

        @keyframes scroll-right {
          0% {
            transform: translateX(-100%);
          }
          100% {
            transform: translateX(0);
          }
        }

        .background-mosaic {
          position: absolute;
          top: -40%;
          left: -20%;
          right: -20%;
          bottom: -20%;
          z-index: 1;
          overflow: hidden;
        }

        .mosaic-row {
          display: flex;
          gap: 8px;
          width: 200%;
          margin-bottom: 8px;
        }

        .mosaic-row:nth-child(odd) {
          animation: scroll-left 90s linear infinite;
        }

        .mosaic-row:nth-child(even) {
          animation: scroll-right 805s linear infinite;
        }

        .mosaic-image {
          flex-shrink: 0;
          width: 120px;
          height: 120px;
          object-fit: cover;
          border-radius: 8px;
          opacity: 0.8;
          transition: opacity 0.3s ease;
        }

        .mosaic-image:hover {
          opacity: 1;
        }

        .content-overlay {
          position: relative;
          z-index: 10;
          background: rgba(0, 0, 0, 0.6);
          backdrop-filter: blur(1px);
        }

        @media (max-width: 768px) {
          .mosaic-image {
            width: 100px;
            height: 100px;
          }
          .mosaic-row {
            gap: 6px;
            margin-bottom: 6px;
          }
          .background-mosaic {
            top: -35%;
          }
        }

        @media (max-width: 640px) {
          .mosaic-image {
            width: 80px;
            height: 80px;
          }
          .mosaic-row {
            gap: 4px;
            margin-bottom: 4px;
          }
          .background-mosaic {
            top: -30%;
          }
        }
      `}</style>

      {/* Background Mosaic Images */}
      <div className="background-mosaic">
        {/* Create multiple rows with alternating directions */}
        {Array.from({ length: 12 }, (_, rowIndex) => (
          <div key={`row-${rowIndex}`} className="mosaic-row">
            {/* Duplicate images for seamless scrolling */}
            {MOSAIC_IMAGES.concat(MOSAIC_IMAGES).map((image, imageIndex) => (
              <img
                key={`row-${rowIndex}-img-${imageIndex}`}
                src={image}
                alt={`Design inspiration ${imageIndex + 1}`}
                className="mosaic-image"
                loading="lazy"
              />
            ))}
          </div>
        ))}

        {/* Light Overlay for Content Readability */}
        <div className="absolute inset-0 bg-gray-950/30"></div>
        <div className="absolute inset-0 bg-gradient-to-b from-gray-950/50 via-gray-950/20 to-gray-950/50"></div>
      </div>

      {/* Content Overlay */}
      <div className="content-overlay min-h-screen">
        <div className="relative z-10 max-w-7xl mx-auto px-4 py-16 lg:py-24">
          <div className="flex flex-col justify-center items-center text-center min-h-[80vh] space-y-8">
            <div className="space-y-6 max-w-4xl">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold text-white leading-tight">
                {title}
              </h1>
              <p className="text-lg sm:text-xl text-gray-200 leading-relaxed">
                {subtitle}
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <a
                href={primaryButtonHref}
                className="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-black bg-teal-400 rounded-lg hover:bg-teal-300 transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-lg"
              >
                {primaryButtonText}
              </a>
              <a
                href={secondaryButtonHref}
                className="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white border-2 border-gray-400 rounded-lg hover:border-teal-400 hover:text-teal-400 transition-all duration-200 backdrop-blur-sm bg-white/10"
              >
                {secondaryButtonText}
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
