"use client";
import React from 'react';

interface HeroSectionProps {
  title?: string;
  subtitle?: string;
  primaryButtonText?: string;
  primaryButtonHref?: string;
  secondaryButtonText?: string;
  secondaryButtonHref?: string;
  className?: string;
}

// Placeholder images for the scrolling animation
const SCROLL_IMAGES_TOP = [
  "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1618221195710-dd6b41faaea6?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1616486338812-3dadae4b4ace?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1615529328331-f8917597711f?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1616137466211-f939a420be84?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1615874959474-d609969a20ed?w=400&h=300&fit=crop",
];

const SCROLL_IMAGES_MIDDLE = [
  "https://images.unsplash.com/photo-1615529328331-f8917597711f?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1616137466211-f939a420be84?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1615874959474-d609969a20ed?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1618221195710-dd6b41faaea6?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1616486338812-3dadae4b4ace?w=400&h=300&fit=crop",
];

const SCROLL_IMAGES_BOTTOM = [
  "https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1618221195710-dd6b41faaea6?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1616486338812-3dadae4b4ace?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1615529328331-f8917597711f?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1616137466211-f939a420be84?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1615874959474-d609969a20ed?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=400&h=300&fit=crop",
];

export const HeroSection: React.FC<HeroSectionProps> = ({
  title = "Professional Production Design",
  subtitle = "Create authentic sets for TV series, movies, and entertainment productions. Professional design tools with 110+ styles and cinematic environments.",
  primaryButtonText = "Start Designing",
  primaryButtonHref = "/design/new",
  secondaryButtonText = "View Gallery",
  secondaryButtonHref = "/gallery",
  className = ""
}) => {
  return (
    <section className={`relative min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950 overflow-hidden ${className}`}>
      <style jsx>{`
        @keyframes scroll-left {
          0% {
            transform: translateX(0) rotate(-8deg);
          }
          100% {
            transform: translateX(-50%) rotate(-8deg);
          }
        }

        @keyframes scroll-right {
          0% {
            transform: translateX(-50%) rotate(-8deg);
          }
          100% {
            transform: translateX(0) rotate(-8deg);
          }
        }

        .scroll-left {
          animation: scroll-left 40s linear infinite;
        }

        .scroll-right {
          animation: scroll-right 35s linear infinite;
        }

        .scroll-container {
          width: 200%;
          transform: rotate(-8deg);
        }

        .scroll-row {
          display: flex;
          gap: 1.5rem;
          width: 100%;
        }

        .scroll-image {
          flex-shrink: 0;
          width: 280px;
          height: 200px;
          border-radius: 16px;
          object-fit: cover;
          opacity: 0.7;
          transition: opacity 0.3s ease;
        }

        .scroll-image:hover {
          opacity: 0.9;
        }

        .background-scrolling {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: 1;
          overflow: hidden;
        }

        .content-overlay {
          position: relative;
          z-index: 10;
          background: rgba(0, 0, 0, 0.4);
          backdrop-filter: blur(1px);
        }

        @media (max-width: 768px) {
          .scroll-image {
            width: 200px;
            height: 140px;
          }
          .scroll-row {
            gap: 1rem;
          }
        }

        @media (max-width: 640px) {
          .scroll-image {
            width: 160px;
            height: 110px;
          }
          .scroll-row {
            gap: 0.75rem;
          }
        }
      `}</style>

      {/* Background Scrolling Images */}
      <div className="background-scrolling">
        {/* Top Row - Scrolling Left */}
        <div className="absolute top-[5%] left-[-10%] right-[-10%] h-[25%] overflow-hidden">
          <div className="scroll-container scroll-left">
            <div className="scroll-row">
              {/* Duplicate images for seamless loop */}
              {[...SCROLL_IMAGES_TOP, ...SCROLL_IMAGES_TOP].map((image, index) => (
                <img
                  key={`top-${index}`}
                  src={image}
                  alt={`Design inspiration ${index + 1}`}
                  className="scroll-image"
                  loading="lazy"
                />
              ))}
            </div>
          </div>
        </div>

        {/* Middle Row - Scrolling Right (slower) */}
        <div className="absolute top-[37.5%] left-[-10%] right-[-10%] h-[25%] overflow-hidden">
          <div className="scroll-container scroll-right" style={{ animationDuration: '45s' }}>
            <div className="scroll-row">
              {/* Duplicate images for seamless loop */}
              {[...SCROLL_IMAGES_MIDDLE, ...SCROLL_IMAGES_MIDDLE].map((image, index) => (
                <img
                  key={`middle-${index}`}
                  src={image}
                  alt={`Design inspiration ${index + 1}`}
                  className="scroll-image"
                  loading="lazy"
                />
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Row - Scrolling Left (different speed) */}
        <div className="absolute bottom-[5%] left-[-10%] right-[-10%] h-[25%] overflow-hidden">
          <div className="scroll-container scroll-left" style={{ animationDuration: '50s' }}>
            <div className="scroll-row">
              {/* Duplicate images for seamless loop */}
              {[...SCROLL_IMAGES_BOTTOM, ...SCROLL_IMAGES_BOTTOM].map((image, index) => (
                <img
                  key={`bottom-${index}`}
                  src={image}
                  alt={`Design inspiration ${index + 1}`}
                  className="scroll-image"
                  loading="lazy"
                />
              ))}
            </div>
          </div>
        </div>

        {/* Gradient Overlays */}
        <div className="absolute inset-0 bg-gradient-to-r from-gray-950/90 via-gray-950/20 to-gray-950/90"></div>
        <div className="absolute inset-0 bg-gradient-to-b from-gray-950/80 via-gray-950/30 to-gray-950/80"></div>
        <div className="absolute inset-0 bg-gray-950/40"></div>
      </div>

      {/* Content Overlay */}
      <div className="content-overlay min-h-screen">
        <div className="relative z-10 max-w-7xl mx-auto px-4 py-16 lg:py-24">
          <div className="flex flex-col justify-center items-center text-center min-h-[80vh] space-y-8">
            <div className="space-y-6 max-w-4xl">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold text-white leading-tight">
                {title}
              </h1>
              <p className="text-lg sm:text-xl text-gray-200 leading-relaxed">
                {subtitle}
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <a
                href={primaryButtonHref}
                className="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-black bg-teal-400 rounded-lg hover:bg-teal-300 transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-lg"
              >
                {primaryButtonText}
              </a>
              <a
                href={secondaryButtonHref}
                className="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white border-2 border-gray-400 rounded-lg hover:border-teal-400 hover:text-teal-400 transition-all duration-200 backdrop-blur-sm bg-white/10"
              >
                {secondaryButtonText}
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
