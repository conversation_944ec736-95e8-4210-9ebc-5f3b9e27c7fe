"use client";
import React from 'react';

interface HeroSectionProps {
  title?: string;
  subtitle?: string;
  primaryButtonText?: string;
  primaryButtonHref?: string;
  secondaryButtonText?: string;
  secondaryButtonHref?: string;
  className?: string;
}

// Diverse images for the mosaic background
const MOSAIC_IMAGES = [
  "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1618221195710-dd6b41faaea6?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1616486338812-3dadae4b4ace?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1615529328331-f8917597711f?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1616137466211-f939a420be84?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1615874959474-d609969a20ed?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1631679706909-1844bbd07221?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1616594039964-ae9021a400a0?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1615529182904-14819c35db37?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1616137466211-f939a420be84?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1615529182904-14819c35db37?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1616594039964-ae9021a400a0?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1631679706909-1844bbd07221?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1615874959474-d609969a20ed?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1616137466211-f939a420be84?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1615529328331-f8917597711f?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1616486338812-3dadae4b4ace?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1618221195710-dd6b41faaea6?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=300&fit=crop",
  "https://images.unsplash.com/photo-1631679706909-1844bbd07221?w=300&h=300&fit=crop",
];

export const HeroSection: React.FC<HeroSectionProps> = ({
  title = "Professional Production Design",
  subtitle = "Create authentic sets for TV series, movies, and entertainment productions. Professional design tools with 110+ styles and cinematic environments.",
  primaryButtonText = "Start Designing",
  primaryButtonHref = "/design/new",
  secondaryButtonText = "View Gallery",
  secondaryButtonHref = "/gallery",
  className = ""
}) => {
  return (
    <section className={`relative min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950 overflow-hidden ${className}`}>
      <style jsx>{`
        @keyframes float-diagonal {
          0% {
            transform: translate(0, 0) rotate(-15deg);
          }
          100% {
            transform: translate(-100px, -100px) rotate(-15deg);
          }
        }

        @keyframes float-diagonal-reverse {
          0% {
            transform: translate(-100px, -100px) rotate(-15deg);
          }
          100% {
            transform: translate(0, 0) rotate(-15deg);
          }
        }

        .background-mosaic {
          position: absolute;
          top: -20%;
          left: -20%;
          right: -20%;
          bottom: -20%;
          z-index: 1;
          overflow: hidden;
          transform: rotate(-15deg);
        }

        .mosaic-grid {
          display: grid;
          grid-template-columns: repeat(8, 1fr);
          grid-template-rows: repeat(6, 1fr);
          gap: 8px;
          width: 120%;
          height: 120%;
          animation: float-diagonal 60s linear infinite;
        }

        .mosaic-grid.reverse {
          animation: float-diagonal-reverse 80s linear infinite;
        }

        .mosaic-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 8px;
          opacity: 0.6;
          transition: opacity 0.3s ease;
        }

        .mosaic-image:hover {
          opacity: 0.8;
        }

        .content-overlay {
          position: relative;
          z-index: 10;
          background: rgba(0, 0, 0, 0.5);
          backdrop-filter: blur(2px);
        }

        @media (max-width: 768px) {
          .mosaic-grid {
            grid-template-columns: repeat(6, 1fr);
            grid-template-rows: repeat(8, 1fr);
            gap: 6px;
          }
          .background-mosaic {
            transform: rotate(-12deg);
          }
        }

        @media (max-width: 640px) {
          .mosaic-grid {
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: repeat(10, 1fr);
            gap: 4px;
          }
          .background-mosaic {
            transform: rotate(-10deg);
          }
        }
      `}</style>

      {/* Background Mosaic Images */}
      <div className="background-mosaic">
        {/* Main Grid */}
        <div className="mosaic-grid">
          {MOSAIC_IMAGES.concat(MOSAIC_IMAGES).map((image, index) => (
            <img
              key={`mosaic-${index}`}
              src={image}
              alt={`Design inspiration ${index + 1}`}
              className="mosaic-image"
              loading="lazy"
            />
          ))}
        </div>

        {/* Overlapping Grid for seamless effect */}
        <div className="mosaic-grid reverse" style={{ position: 'absolute', top: '50%', left: '50%' }}>
          {MOSAIC_IMAGES.slice().reverse().concat(MOSAIC_IMAGES.slice().reverse()).map((image, index) => (
            <img
              key={`mosaic-overlay-${index}`}
              src={image}
              alt={`Design inspiration ${index + 1}`}
              className="mosaic-image"
              loading="lazy"
            />
          ))}
        </div>

        {/* Dark Overlay for Content Readability */}
        <div className="absolute inset-0 bg-gray-950/70"></div>
        <div className="absolute inset-0 bg-gradient-to-b from-gray-950/90 via-gray-950/50 to-gray-950/90"></div>
      </div>

      {/* Content Overlay */}
      <div className="content-overlay min-h-screen">
        <div className="relative z-10 max-w-7xl mx-auto px-4 py-16 lg:py-24">
          <div className="flex flex-col justify-center items-center text-center min-h-[80vh] space-y-8">
            <div className="space-y-6 max-w-4xl">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold text-white leading-tight">
                {title}
              </h1>
              <p className="text-lg sm:text-xl text-gray-200 leading-relaxed">
                {subtitle}
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <a
                href={primaryButtonHref}
                className="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-black bg-teal-400 rounded-lg hover:bg-teal-300 transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-lg"
              >
                {primaryButtonText}
              </a>
              <a
                href={secondaryButtonHref}
                className="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white border-2 border-gray-400 rounded-lg hover:border-teal-400 hover:text-teal-400 transition-all duration-200 backdrop-blur-sm bg-white/10"
              >
                {secondaryButtonText}
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
