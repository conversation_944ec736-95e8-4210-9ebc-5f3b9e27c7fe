"use client";
import React from 'react';

interface HeroSectionProps {
  title?: string;
  subtitle?: string;
  primaryButtonText?: string;
  primaryButtonHref?: string;
  secondaryButtonText?: string;
  secondaryButtonHref?: string;
  className?: string;
}

// Placeholder images for the scrolling animation
const SCROLL_IMAGES = [
  "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop",
  "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=400&h=300&fit=crop",
];

export const HeroSection: React.FC<HeroSectionProps> = ({
  title = "Professional Production Design",
  subtitle = "Create authentic sets for TV series, movies, and entertainment productions. Professional design tools with 110+ styles and cinematic environments.",
  primaryButtonText = "Start Designing",
  primaryButtonHref = "/design/new",
  secondaryButtonText = "View Gallery",
  secondaryButtonHref = "/gallery",
  className = ""
}) => {
  return (
    <section className={`relative min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950 overflow-hidden ${className}`}>
      <style jsx>{`
        @keyframes scroll-left {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-50%);
          }
        }
        
        @keyframes scroll-right {
          0% {
            transform: translateX(-50%);
          }
          100% {
            transform: translateX(0);
          }
        }
        
        .scroll-left {
          animation: scroll-left 30s linear infinite;
        }
        
        .scroll-right {
          animation: scroll-right 30s linear infinite;
        }
        
        .scroll-container {
          width: 200%;
        }
        
        .scroll-row {
          display: flex;
          gap: 1rem;
          width: 100%;
        }
        
        .scroll-image {
          flex-shrink: 0;
          width: 200px;
          height: 150px;
          border-radius: 12px;
          object-fit: cover;
        }
        
        @media (max-width: 768px) {
          .scroll-image {
            width: 150px;
            height: 112px;
          }
        }
        
        @media (max-width: 640px) {
          .scroll-image {
            width: 120px;
            height: 90px;
          }
        }
      `}</style>
      
      <div className="relative z-10 max-w-7xl mx-auto px-4 py-16 lg:py-24">
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center min-h-[80vh]">
          {/* Left Half - Content */}
          <div className="flex flex-col justify-center space-y-8">
            <div className="space-y-6">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold text-white leading-tight">
                {title}
              </h1>
              <p className="text-lg sm:text-xl text-gray-300 max-w-2xl leading-relaxed">
                {subtitle}
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4">
              <a
                href={primaryButtonHref}
                className="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-black bg-teal-400 rounded-lg hover:bg-teal-300 transition-all duration-200 transform hover:scale-105 active:scale-95"
              >
                {primaryButtonText}
              </a>
              <a
                href={secondaryButtonHref}
                className="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white border-2 border-gray-600 rounded-lg hover:border-teal-400 hover:text-teal-400 transition-all duration-200"
              >
                {secondaryButtonText}
              </a>
            </div>
          </div>
          
          {/* Right Half - Scrolling Images */}
          <div className="relative h-[600px] lg:h-[700px] overflow-hidden">
            {/* Top Row - Scrolling Left */}
            <div className="absolute top-0 left-0 right-0 h-[45%] overflow-hidden">
              <div className="scroll-container scroll-left">
                <div className="scroll-row">
                  {/* Duplicate images for seamless loop */}
                  {[...SCROLL_IMAGES, ...SCROLL_IMAGES].map((image, index) => (
                    <img
                      key={`top-${index}`}
                      src={image}
                      alt={`Design inspiration ${index + 1}`}
                      className="scroll-image"
                      loading="lazy"
                    />
                  ))}
                </div>
              </div>
            </div>
            
            {/* Bottom Row - Scrolling Right */}
            <div className="absolute bottom-0 left-0 right-0 h-[45%] overflow-hidden">
              <div className="scroll-container scroll-right">
                <div className="scroll-row">
                  {/* Duplicate images for seamless loop */}
                  {[...SCROLL_IMAGES, ...SCROLL_IMAGES].map((image, index) => (
                    <img
                      key={`bottom-${index}`}
                      src={image}
                      alt={`Design inspiration ${index + 1}`}
                      className="scroll-image"
                      loading="lazy"
                    />
                  ))}
                </div>
              </div>
            </div>
            
            {/* Gradient Overlays for smooth edges */}
            <div className="absolute top-0 left-0 w-16 h-full bg-gradient-to-r from-gray-950 to-transparent z-10"></div>
            <div className="absolute top-0 right-0 w-16 h-full bg-gradient-to-l from-gray-950 to-transparent z-10"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
