import "@/app/globals.css";
import React from "react";
import ClientBody from "./ClientBody";
import { AltoraLogo } from "../components/AltoraLogo";

const POLYSANS_FONT_URLS = [
  {
    url: "https://ext.same-assets.com/4123950039/3713092101.ttf",
    weight: "400",
    style: "normal",
  },
  {
    url: "https://ext.same-assets.com/4123950039/1287537253.ttf",
    weight: "500",
    style: "normal",
  },
  {
    url: "https://ext.same-assets.com/4123950039/523861748.ttf",
    weight: "700",
    style: "normal",
  },
];

export const metadata = {
  title: "Altora – AI Production Design Studio",
  description:
    "Professional design tools for TV series, movies, and entertainment productions. 110+ styles, authentic materials, and cinematic environments. Free to try.",
};

const NAV_LINKS = [
  { label: "Pricing", href: "/pricing" },
  { label: "Glossary", href: "/interior-design-glossary" },
  { label: "Advice", href: "/advice" },
  { label: "Podcast", href: "/podcast" },
];

function Header() {
  return (
    <header className="sticky top-0 z-30 w-full bg-black/95 backdrop-blur-lg">
      <nav className="flex items-center justify-between px-6 py-4 max-w-7xl mx-auto">
        <a href="/" className="flex items-center gap-2">
          <AltoraLogo size="md" />
        </a>
        <div className="flex gap-4 items-center">
          <a
            href="/pricing"
            className="text-gray-400 hover:text-white transition-colors text-sm font-medium"
          >
            Pricing
          </a>
          <a
            href="/start"
            className="text-gray-400 hover:text-white transition-colors text-sm font-medium"
          >
            Log in
          </a>
          <a
            href="/start"
            className="px-4 py-2 rounded bg-[#2dd4bf] text-black font-semibold hover:bg-[#14b8a6] transition-all text-sm"
          >
            Design your interior
          </a>
        </div>
      </nav>
    </header>
  );
}

function Footer() {
  return (
    <footer className="p-8 sm:p-12 border-t border-gray-800 text-gray-400 bg-gray-900 mt-24">
      <section className="max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-4 gap-8">
        <div>
          <p className="text-lg text-gray-100 my-4">
            Generate interior designs with AI.
          </p>
          <p className="mt-2">© 2022-2025 All rights reserved</p>
          <div className="text-gray-300 text-sm">
            Room AI® is a registered trademark.
            <br />
            Killbridge Ventures Pte. Ltd.
          </div>
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-100">Resources</h3>
          <ul className="flex flex-col gap-1 mt-2">
            <li>
              <a
                className="hover:text-white hover:underline"
                href="/advice"
              >
                Interior Design Advice
              </a>
            </li>
            <li>
              <a
                className="hover:text-white hover:underline"
                href="/interior-design-glossary"
              >
                Glossary
              </a>
            </li>
            <li>
              <a
                className="hover:text-white hover:underline"
                href="/podcast"
              >
                Podcast
              </a>
            </li>
          </ul>
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-100">Links</h3>
          <ul className="flex flex-col gap-1 mt-2">
            <li>
              <a
                className="hover:text-white hover:underline"
                href="/pricing"
              >
                Pricing
              </a>
            </li>
            <li>
              <a
                className="hover:text-white hover:underline"
                href="/start"
              >
                Log in
              </a>
            </li>
            <li>
              <a
                className="hover:text-white hover:underline"
                href="/billing"
              >
                Billing & Invoices
              </a>
            </li>
            <li>
              <a
                className="hover:text-white hover:underline"
                href="/terms-of-service"
              >
                Terms of Service
              </a>
            </li>
            <li>
              <a
                className="hover:text-white hover:underline"
                href="/privacy-policy"
              >
                Privacy Policy
              </a>
            </li>
          </ul>
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-100">Contact</h3>
          <ul className="flex flex-col gap-1 mt-2">
            <li>
              <a
                className="hover:text-white hover:underline"
                href="https://twitter.com/roomai_com"
                target="_blank"
                rel="noopener noreferrer"
              >
                Twitter
              </a>
            </li>
            <li>
              <a
                className="hover:text-white hover:underline"
                href="mailto:<EMAIL>"
              >
                Support (email)
              </a>
            </li>
            <li>
              <a
                className="hover:text-white hover:underline"
                href="mailto:<EMAIL>"
              >
                Press (email)
              </a>
            </li>
          </ul>
        </div>
      </section>
    </footer>
  );
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head />
      <body className="bg-gray-950 text-white font-sans min-h-screen flex flex-col">
        <Header />
        <ClientBody>{children}</ClientBody>
        <Footer />
      </body>
    </html>
  );
}
